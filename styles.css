/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #ffffff;
}

/* Header Styles */
.header {
  background: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
}

.logo-img {
  height: 40px;
  width: auto;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #2563eb;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 1rem;
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
}

.dropdown-menu li {
  margin-bottom: 0.5rem;
}

.dropdown-menu a {
  color: #666;
  text-decoration: none;
  font-size: 0.9rem;
}

.phone-link {
  color: #2563eb;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 8rem 2rem 4rem;
  margin-top: 80px;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-badges {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.hero-badge {
  width: 60px;
  height: 60px;
  background: #e2e8f0;
  border-radius: 8px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #64748b;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.cta-button {
  display: inline-block;
  background: #2563eb;
  color: white;
  padding: 1rem 2rem;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: background 0.3s ease;
}

.cta-button:hover {
  background: #1d4ed8;
}

.hero-images {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.hero-image {
  width: 100%;
  height: 200px;
  background: #e2e8f0;
  border-radius: 12px;
  object-fit: cover;
}

/* Stats Section */
.stats {
  padding: 4rem 2rem;
  background: white;
}

.stats-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin-bottom: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  color: #64748b;
  font-weight: 500;
}

.stats-description {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  font-size: 1.1rem;
  color: #64748b;
}

/* Services Section */
.services {
  padding: 4rem 2rem;
  background: #f8fafc;
}

.services-container {
  max-width: 1200px;
  margin: 0 auto;
}

.services-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
}

.services-subtitle {
  font-size: 1.2rem;
  color: #64748b;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.services-description {
  font-size: 1.1rem;
  color: #64748b;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.services-link {
  color: #2563eb;
  text-decoration: none;
  font-weight: 600;
  margin-bottom: 3rem;
  display: inline-block;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.service-item {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.service-icon {
  width: 60px;
  height: 60px;
  background: #e2e8f0;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.service-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.service-description {
  color: #64748b;
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  position: relative;
  padding: 4rem 2rem;
  background: #2563eb;
  text-align: center;
}

.cta-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.cta-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cta-content {
  position: relative;
  z-index: 2;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 2rem;
}

.cta-section .cta-button {
  background: white;
  color: #2563eb;
}

.cta-section .cta-button:hover {
  background: #f1f5f9;
}

/* Features Section */
.features {
  padding: 4rem 2rem;
  background: white;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3rem;
}

.feature-item {
  text-align: left;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.feature-description {
  color: #64748b;
  line-height: 1.6;
}

/* About Section */
.about {
  padding: 4rem 2rem;
  background: #f8fafc;
}

.about-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: left;
}

.about-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
}

.about-subtitle {
  font-size: 1.2rem;
  color: #64748b;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.about-description {
  font-size: 1.1rem;
  color: #64748b;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.about-link {
  color: #2563eb;
  text-decoration: none;
  font-weight: 600;
}

/* Testimonials Section */
.testimonials {
  padding: 4rem 2rem;
  background: white;
  text-align: center;
}

.testimonials-container {
  max-width: 800px;
  margin: 0 auto;
}

.testimonials-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 2rem;
}

/* FAQ Section */
.faq {
  padding: 4rem 2rem;
  background: #f8fafc;
}

.faq-container {
  max-width: 800px;
  margin: 0 auto;
}

.faq-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  text-align: center;
}

.faq-subtitle {
  text-align: center;
  color: #64748b;
  margin-bottom: 3rem;
  font-size: 1.1rem;
}

.faq-item {
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.faq-question {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.faq-answer {
  color: #64748b;
  line-height: 1.6;
}

/* Final CTA Section */
.final-cta {
  position: relative;
  padding: 4rem 2rem;
  background: #2563eb;
  text-align: center;
}

.final-cta-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.final-cta-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.final-cta-content {
  position: relative;
  z-index: 2;
}

.final-cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 2rem;
}

.final-cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.final-cta .cta-button {
  background: white;
  color: #2563eb;
}

.final-cta .cta-button:hover {
  background: #f1f5f9;
}

/* Footer */
.footer {
  background: #1e293b;
  color: white;
  padding: 4rem 2rem 2rem;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: 3rem;
}

.footer-brand {
  max-width: 300px;
}

.footer-logo {
  height: 40px;
  width: auto;
  margin-bottom: 1.5rem;
  background: #e2e8f0;
  border-radius: 4px;
}

.footer-description {
  color: #94a3b8;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.footer-legal {
  font-size: 0.9rem;
}

.footer-legal a {
  color: #94a3b8;
  text-decoration: none;
}

.footer-legal a:hover {
  color: white;
}

.footer-heading {
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1.5rem;
  letter-spacing: 0.05em;
}

.footer-list {
  list-style: none;
}

.footer-list li {
  margin-bottom: 0.75rem;
}

.footer-list a {
  color: #94a3b8;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-list a:hover {
  color: white;
}

.footer-hours {
  margin-top: 2rem;
}

.footer-hours p {
  color: #94a3b8;
  font-size: 0.9rem;
  line-height: 1.4;
}

.footer-bottom {
  border-top: 1px solid #334155;
  margin-top: 3rem;
  padding-top: 2rem;
  text-align: center;
}

.footer-bottom p {
  color: #94a3b8;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-container {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .features-container {
    grid-template-columns: 1fr;
  }

  .nav-menu {
    display: none;
  }

  .final-cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .footer-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .footer-brand {
    max-width: none;
  }
}
